import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Cart store
export const cartItems = writable([]);
export const cartTotal = writable(0);
export const cartCount = writable(0);

// Initialize cart from localStorage
if (browser) {
	const savedCart = localStorage.getItem('cart');
	if (savedCart) {
		try {
			const cart = JSON.parse(savedCart);
			cartItems.set(cart);
			updateCartTotals(cart);
		} catch (e) {
			console.error('Error parsing cart from localStorage:', e);
		}
	}
}

// Subscribe to cart changes and save to localStorage
cartItems.subscribe(items => {
	if (browser) {
		localStorage.setItem('cart', JSON.stringify(items));
		updateCartTotals(items);
	}
});

// Update cart totals
function updateCartTotals(items) {
	const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
	const count = items.reduce((sum, item) => sum + item.quantity, 0);
	
	cartTotal.set(total);
	cartCount.set(count);
}

// Cart functions
export const addToCart = (product, quantity = 1) => {
	cartItems.update(items => {
		const existingItem = items.find(item => item.id === product.id);
		
		if (existingItem) {
			existingItem.quantity += quantity;
		} else {
			items.push({
				id: product.id,
				name: product.name,
				price: product.price,
				image: product.image,
				quantity: quantity
			});
		}
		
		return items;
	});
};

export const removeFromCart = (productId) => {
	cartItems.update(items => items.filter(item => item.id !== productId));
};

export const updateQuantity = (productId, quantity) => {
	if (quantity <= 0) {
		removeFromCart(productId);
		return;
	}
	
	cartItems.update(items => {
		const item = items.find(item => item.id === productId);
		if (item) {
			item.quantity = quantity;
		}
		return items;
	});
};

export const clearCart = () => {
	cartItems.set([]);
};
