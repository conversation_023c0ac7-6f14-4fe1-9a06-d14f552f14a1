import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Language store
export const currentLanguage = writable('en');

// Available languages
export const languages = {
	en: {
		code: 'en',
		name: 'English',
		dir: 'ltr'
	},
	ku: {
		code: 'ku',
		name: 'کوردی سۆرانی',
		dir: 'rtl'
	}
};

// Initialize language from localStorage or default to English
if (browser) {
	const savedLanguage = localStorage.getItem('language') || 'en';
	currentLanguage.set(savedLanguage);
	
	// Set document direction
	currentLanguage.subscribe(lang => {
		if (typeof document !== 'undefined') {
			document.documentElement.dir = languages[lang]?.dir || 'ltr';
			document.documentElement.lang = lang;
			localStorage.setItem('language', lang);
		}
	});
}

// Language switching function
export const setLanguage = (lang) => {
	if (languages[lang]) {
		currentLanguage.set(lang);
	}
};

// Translation function (basic implementation)
export const t = (key, lang = 'en') => {
	const translations = {
		en: {
			// Navigation
			'nav.home': 'Home',
			'nav.products': 'Products',
			'nav.categories': 'Categories',
			'nav.cart': 'Cart',
			'nav.account': 'Account',
			'nav.admin': 'Admin',
			'nav.login': 'Login',
			'nav.register': 'Register',
			'nav.logout': 'Logout',
			
			// Common
			'common.loading': 'Loading...',
			'common.error': 'Error',
			'common.success': 'Success',
			'common.save': 'Save',
			'common.cancel': 'Cancel',
			'common.delete': 'Delete',
			'common.edit': 'Edit',
			'common.add': 'Add',
			'common.search': 'Search',
			'common.filter': 'Filter',
			'common.sort': 'Sort',
			'common.price': 'Price',
			'common.quantity': 'Quantity',
			'common.total': 'Total',
			
			// Auth
			'auth.email': 'Email',
			'auth.password': 'Password',
			'auth.confirmPassword': 'Confirm Password',
			'auth.firstName': 'First Name',
			'auth.lastName': 'Last Name',
			'auth.phone': 'Phone Number',
			'auth.signIn': 'Sign In',
			'auth.signUp': 'Sign Up',
			'auth.forgotPassword': 'Forgot Password?',
			'auth.resetPassword': 'Reset Password',
			'auth.checkEmail': 'Please check your email for confirmation',
			
			// Products
			'product.addToCart': 'Add to Cart',
			'product.outOfStock': 'Out of Stock',
			'product.inStock': 'In Stock',
			'product.description': 'Description',
			'product.specifications': 'Specifications',
			'product.reviews': 'Reviews',
			
			// Cart
			'cart.empty': 'Your cart is empty',
			'cart.checkout': 'Checkout',
			'cart.continue': 'Continue Shopping',
			'cart.remove': 'Remove',
			'cart.update': 'Update',
			
			// Footer
			'footer.company': 'Coco UK',
			'footer.description': 'Your trusted online store',
			'footer.contact': 'Contact Us',
			'footer.about': 'About Us',
			'footer.privacy': 'Privacy Policy',
			'footer.terms': 'Terms of Service'
		},
		ku: {
			// Navigation
			'nav.home': 'سەرەتا',
			'nav.products': 'بەرهەمەکان',
			'nav.categories': 'جۆرەکان',
			'nav.cart': 'سەبەتە',
			'nav.account': 'هەژمار',
			'nav.admin': 'بەڕێوەبەر',
			'nav.login': 'چوونەژوورەوە',
			'nav.register': 'تۆمارکردن',
			'nav.logout': 'چوونەدەرەوە',
			
			// Common
			'common.loading': 'بارکردن...',
			'common.error': 'هەڵە',
			'common.success': 'سەرکەوتوو',
			'common.save': 'پاشەکەوتکردن',
			'common.cancel': 'هەڵوەشاندنەوە',
			'common.delete': 'سڕینەوە',
			'common.edit': 'دەستکاریکردن',
			'common.add': 'زیادکردن',
			'common.search': 'گەڕان',
			'common.filter': 'پاڵاوتن',
			'common.sort': 'ڕیزکردن',
			'common.price': 'نرخ',
			'common.quantity': 'بڕ',
			'common.total': 'کۆی گشتی',
			
			// Auth
			'auth.email': 'ئیمەیڵ',
			'auth.password': 'وشەی نهێنی',
			'auth.confirmPassword': 'دووپاتکردنەوەی وشەی نهێنی',
			'auth.firstName': 'ناوی یەکەم',
			'auth.lastName': 'ناوی کۆتایی',
			'auth.phone': 'ژمارەی تەلەفۆن',
			'auth.signIn': 'چوونەژوورەوە',
			'auth.signUp': 'تۆمارکردن',
			'auth.forgotPassword': 'وشەی نهێنیت لەبیرچووە؟',
			'auth.resetPassword': 'ڕێکخستنەوەی وشەی نهێنی',
			'auth.checkEmail': 'تکایە ئیمەیڵەکەت بپشکنە بۆ پشتڕاستکردنەوە',
			
			// Products
			'product.addToCart': 'زیادکردن بۆ سەبەتە',
			'product.outOfStock': 'لە کۆگا نییە',
			'product.inStock': 'لە کۆگا هەیە',
			'product.description': 'وەسف',
			'product.specifications': 'تایبەتمەندییەکان',
			'product.reviews': 'هەڵسەنگاندنەکان',
			
			// Cart
			'cart.empty': 'سەبەتەکەت بەتاڵە',
			'cart.checkout': 'پارەدان',
			'cart.continue': 'بەردەوامبوون لە کڕین',
			'cart.remove': 'لابردن',
			'cart.update': 'نوێکردنەوە',
			
			// Footer
			'footer.company': 'کۆکۆ یوکەی',
			'footer.description': 'فرۆشگای ئۆنلاینی متمانەپێکراو',
			'footer.contact': 'پەیوەندیمان پێوە بکە',
			'footer.about': 'دەربارەمان',
			'footer.privacy': 'سیاسەتی تایبەتێتی',
			'footer.terms': 'مەرجەکانی خزمەتگوزاری'
		}
	};
	
	return translations[lang]?.[key] || key;
};
