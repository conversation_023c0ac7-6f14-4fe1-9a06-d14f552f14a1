// @ts-nocheck
import { writable } from 'svelte/store';
import { supabase } from '$lib/supabaseClient.js';

export const user = writable(null);
export const loading = writable(true);

// Initialize auth state
supabase.auth.getSession().then(({ data: { session } }) => {
	user.set(session?.user ?? null);
	loading.set(false);
});

// Listen for auth changes
supabase.auth.onAuthStateChange((event, session) => {
	user.set(session?.user ?? null);
	loading.set(false);
});

// Auth functions
export const signUp = async (email, password, userData = {}) => {
	const { data, error } = await supabase.auth.signUp({
		email,
		password,
		options: {
			data: userData
		}
	});
	return { data, error };
};

export const signIn = async (email, password) => {
	const { data, error } = await supabase.auth.signInWithPassword({
		email,
		password
	});
	return { data, error };
};

export const signOut = async () => {
	const { error } = await supabase.auth.signOut();
	return { error };
};

export const resetPassword = async (email) => {
	const { data, error } = await supabase.auth.resetPasswordForEmail(email);
	return { data, error };
};

// Check if user is admin
export const isAdmin = (userEmail) => {
	return userEmail === '<EMAIL>';
};
