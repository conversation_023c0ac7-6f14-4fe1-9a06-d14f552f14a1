<script lang="ts">
	import { goto } from '$app/navigation';
	import { signIn } from '$lib/stores/auth.js';
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { Mail, Lock, Eye, EyeOff } from 'lucide-svelte';
	
	let email = '';
	let password = '';
	let showPassword = false;
	let loading = false;
	let error = '';
	
	async function handleSubmit() {
		if (!email || !password) {
			error = 'Please fill in all fields';
			return;
		}
		
		loading = true;
		error = '';
		
		try {
			const { data, error: authError } = await signIn(email, password);
			
			if (authError) {
				error = authError.message;
			} else if (data.user) {
				// Redirect to home page or intended destination
				goto('/');
			}
		} catch (err) {
			error = 'An unexpected error occurred';
		} finally {
			loading = false;
		}
	}
</script>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full space-y-8">
		<div>
			<div class="flex justify-center">
				<img src="/logo/logo.svg" alt="Coco UK" class="h-12 w-12" />
			</div>
			<h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
				{#if $currentLanguage === 'en'}
					Sign in to your account
				{:else}
					چوونەژوورەوە بۆ هەژمارەکەت
				{/if}
			</h2>
			<p class="mt-2 text-center text-sm text-gray-600">
				{#if $currentLanguage === 'en'}
					Or
				{:else}
					یان
				{/if}
				<a href="/register" class="font-medium text-blue-600 hover:text-blue-500">
					{#if $currentLanguage === 'en'}
						create a new account
					{:else}
						هەژمارێکی نوێ دروستبکە
					{/if}
				</a>
			</p>
		</div>
		
		<form class="mt-8 space-y-6" on:submit|preventDefault={handleSubmit}>
			{#if error}
				<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
					{error}
				</div>
			{/if}
			
			<div class="space-y-4">
				<!-- Email -->
				<div>
					<label for="email" class="block text-sm font-medium text-gray-700">
						{t('auth.email', $currentLanguage)}
					</label>
					<div class="mt-1 relative">
						<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<Mail class="h-5 w-5 text-gray-400" />
						</div>
						<input
							id="email"
							name="email"
							type="email"
							autocomplete="email"
							required
							bind:value={email}
							class="appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
							placeholder={t('auth.email', $currentLanguage)}
						/>
					</div>
				</div>
				
				<!-- Password -->
				<div>
					<label for="password" class="block text-sm font-medium text-gray-700">
						{t('auth.password', $currentLanguage)}
					</label>
					<div class="mt-1 relative">
						<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<Lock class="h-5 w-5 text-gray-400" />
						</div>
						<input
							id="password"
							name="password"
							type={showPassword ? 'text' : 'password'}
							autocomplete="current-password"
							required
							bind:value={password}
							class="appearance-none relative block w-full pl-10 pr-10 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
							placeholder={t('auth.password', $currentLanguage)}
						/>
						<button
							type="button"
							class="absolute inset-y-0 right-0 pr-3 flex items-center"
							on:click={() => showPassword = !showPassword}
						>
							{#if showPassword}
								<EyeOff class="h-5 w-5 text-gray-400" />
							{:else}
								<Eye class="h-5 w-5 text-gray-400" />
							{/if}
						</button>
					</div>
				</div>
			</div>
			
			<div class="flex items-center justify-between">
				<div class="text-sm">
					<a href="/forgot-password" class="font-medium text-blue-600 hover:text-blue-500">
						{t('auth.forgotPassword', $currentLanguage)}
					</a>
				</div>
			</div>
			
			<div>
				<button
					type="submit"
					disabled={loading}
					class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{#if loading}
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
					{:else}
						{t('auth.signIn', $currentLanguage)}
					{/if}
				</button>
			</div>
		</form>
	</div>
</div>
