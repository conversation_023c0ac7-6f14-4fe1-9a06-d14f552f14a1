<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { user } from '$lib/stores/auth.js';
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { Package, Users, ShoppingCart, TrendingUp, Plus, Edit, Trash2 } from 'lucide-svelte';
	
	// Check if user is admin
	$: if ($user && $user.email !== '<EMAIL>') {
		goto('/');
	}
	
	// Sample admin data (in a real app, this would come from Supabase)
	let stats = {
		totalProducts: 6,
		totalUsers: 12,
		totalOrders: 34,
		totalRevenue: 2450.50
	};
	
	let recentOrders = [
		{ id: 1, customer: '<PERSON>', total: 89.99, status: 'completed', date: '2024-01-15' },
		{ id: 2, customer: '<PERSON>', total: 45.50, status: 'pending', date: '2024-01-14' },
		{ id: 3, customer: '<PERSON>', total: 120.00, status: 'shipped', date: '2024-01-13' }
	];
	
	let products = [
		{ id: 1, name: 'Premium Headphones', price: 89.99, stock: 15, status: 'active' },
		{ id: 2, name: 'Organic Coffee Beans', price: 24.99, stock: 8, status: 'active' },
		{ id: 3, name: 'Yoga Mat', price: 34.99, stock: 0, status: 'out_of_stock' }
	];
	
	function getStatusColor(status: string) {
		switch (status) {
			case 'completed':
			case 'active':
				return 'text-green-600 bg-green-100';
			case 'pending':
				return 'text-yellow-600 bg-yellow-100';
			case 'shipped':
				return 'text-blue-600 bg-blue-100';
			case 'out_of_stock':
				return 'text-red-600 bg-red-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	}
</script>

{#if !$user}
	<div class="min-h-screen flex items-center justify-center">
		<div class="text-center">
			<h1 class="text-2xl font-bold text-gray-900 mb-4">
				{#if $currentLanguage === 'en'}
					Please log in to access admin panel
				{:else}
					تکایە بچۆژوورەوە بۆ دەستپێگەیشتن بە پانێڵی بەڕێوەبەر
				{/if}
			</h1>
			<a href="/login" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700">
				{#if $currentLanguage === 'en'}
					Login
				{:else}
					چوونەژوورەوە
				{/if}
			</a>
		</div>
	</div>
{:else if $user.email !== '<EMAIL>'}
	<div class="min-h-screen flex items-center justify-center">
		<div class="text-center">
			<h1 class="text-2xl font-bold text-red-600 mb-4">
				{#if $currentLanguage === 'en'}
					Access Denied
				{:else}
					دەستپێگەیشتن ڕەتکرایەوە
				{/if}
			</h1>
			<p class="text-gray-600 mb-6">
				{#if $currentLanguage === 'en'}
					You don't have permission to access the admin panel.
				{:else}
					تۆ مۆڵەتت نییە بۆ دەستپێگەیشتن بە پانێڵی بەڕێوەبەر.
				{/if}
			</p>
			<a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700">
				{#if $currentLanguage === 'en'}
					Go Home
				{:else}
					گەڕانەوە بۆ سەرەتا
				{/if}
			</a>
		</div>
	</div>
{:else}
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
		<!-- Page Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900 mb-4">
				{#if $currentLanguage === 'en'}
					Admin Dashboard
				{:else}
					داشبۆردی بەڕێوەبەر
				{/if}
			</h1>
			<p class="text-lg text-gray-600">
				{#if $currentLanguage === 'en'}
					Welcome back, {$user.user_metadata?.first_name || 'Admin'}! Here's what's happening with your store.
				{:else}
					بەخێربێیتەوە، {$user.user_metadata?.first_name || 'بەڕێوەبەر'}! ئەمە ئەوەیە کە لە فرۆشگاکەتدا ڕوودەدات.
				{/if}
			</p>
		</div>
		
		<!-- Stats Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
			<!-- Total Products -->
			<div class="bg-white rounded-lg shadow-md p-6">
				<div class="flex items-center">
					<div class="bg-blue-100 p-3 rounded-full">
						<Package class="text-blue-600" size={24} />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							{#if $currentLanguage === 'en'}
								Total Products
							{:else}
								کۆی بەرهەمەکان
							{/if}
						</p>
						<p class="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
					</div>
				</div>
			</div>
			
			<!-- Total Users -->
			<div class="bg-white rounded-lg shadow-md p-6">
				<div class="flex items-center">
					<div class="bg-green-100 p-3 rounded-full">
						<Users class="text-green-600" size={24} />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							{#if $currentLanguage === 'en'}
								Total Users
							{:else}
								کۆی بەکارهێنەران
							{/if}
						</p>
						<p class="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
					</div>
				</div>
			</div>
			
			<!-- Total Orders -->
			<div class="bg-white rounded-lg shadow-md p-6">
				<div class="flex items-center">
					<div class="bg-purple-100 p-3 rounded-full">
						<ShoppingCart class="text-purple-600" size={24} />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							{#if $currentLanguage === 'en'}
								Total Orders
							{:else}
								کۆی داواکاریەکان
							{/if}
						</p>
						<p class="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
					</div>
				</div>
			</div>
			
			<!-- Total Revenue -->
			<div class="bg-white rounded-lg shadow-md p-6">
				<div class="flex items-center">
					<div class="bg-yellow-100 p-3 rounded-full">
						<TrendingUp class="text-yellow-600" size={24} />
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							{#if $currentLanguage === 'en'}
								Total Revenue
							{:else}
								کۆی داهات
							{/if}
						</p>
						<p class="text-2xl font-bold text-gray-900">£{stats.totalRevenue.toFixed(2)}</p>
					</div>
				</div>
			</div>
		</div>
		
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
			<!-- Recent Orders -->
			<div class="bg-white rounded-lg shadow-md">
				<div class="px-6 py-4 border-b border-gray-200">
					<h2 class="text-lg font-semibold">
						{#if $currentLanguage === 'en'}
							Recent Orders
						{:else}
							داواکاریە نوێیەکان
						{/if}
					</h2>
				</div>
				<div class="p-6">
					<div class="space-y-4">
						{#each recentOrders as order}
							<div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
								<div>
									<p class="font-medium text-gray-900">#{order.id} - {order.customer}</p>
									<p class="text-sm text-gray-600">{order.date}</p>
								</div>
								<div class="text-right">
									<p class="font-semibold text-gray-900">£{order.total.toFixed(2)}</p>
									<span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getStatusColor(order.status)}">
										{order.status}
									</span>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
			
			<!-- Product Management -->
			<div class="bg-white rounded-lg shadow-md">
				<div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
					<h2 class="text-lg font-semibold">
						{#if $currentLanguage === 'en'}
							Product Management
						{:else}
							بەڕێوەبردنی بەرهەمەکان
						{/if}
					</h2>
					<button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 flex items-center space-x-2">
						<Plus size={16} />
						<span>
							{#if $currentLanguage === 'en'}
								Add Product
							{:else}
								زیادکردنی بەرهەم
							{/if}
						</span>
					</button>
				</div>
				<div class="p-6">
					<div class="space-y-4">
						{#each products as product}
							<div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
								<div>
									<p class="font-medium text-gray-900">{product.name}</p>
									<p class="text-sm text-gray-600">
										{#if $currentLanguage === 'en'}
											Stock: {product.stock} | Price: £{product.price.toFixed(2)}
										{:else}
											کۆگا: {product.stock} | نرخ: £{product.price.toFixed(2)}
										{/if}
									</p>
								</div>
								<div class="flex items-center space-x-2">
									<span class="inline-flex px-2 py-1 text-xs font-medium rounded-full {getStatusColor(product.status)}">
										{product.status}
									</span>
									<button class="text-blue-600 hover:text-blue-800 p-1">
										<Edit size={16} />
									</button>
									<button class="text-red-600 hover:text-red-800 p-1">
										<Trash2 size={16} />
									</button>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		</div>
		
		<!-- Quick Actions -->
		<div class="mt-8 bg-white rounded-lg shadow-md p-6">
			<h2 class="text-lg font-semibold mb-4">
				{#if $currentLanguage === 'en'}
					Quick Actions
				{:else}
					کردارە خێراکان
				{/if}
			</h2>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
				<button class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700">
					{#if $currentLanguage === 'en'}
						Add New Product
					{:else}
						زیادکردنی بەرهەمی نوێ
					{/if}
				</button>
				<button class="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700">
					{#if $currentLanguage === 'en'}
						View All Orders
					{:else}
						بینینی هەموو داواکاریەکان
					{/if}
				</button>
				<button class="bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700">
					{#if $currentLanguage === 'en'}
						Manage Categories
					{:else}
						بەڕێوەبردنی جۆرەکان
					{/if}
				</button>
			</div>
		</div>
	</div>
{/if}
