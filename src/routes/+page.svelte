<script lang="ts">
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { ShoppingBag, Truck, Shield, Star } from 'lucide-svelte';
</script>

<div class="bg-white">
	<!-- Hero Section -->
	<div class="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
			<div class="text-center">
				<h1 class="text-4xl md:text-6xl font-bold mb-6">
					{#if $currentLanguage === 'en'}
						Welcome to Coco UK
					{:else}
						بەخێربێن بۆ کۆکۆ یوکەی
					{/if}
				</h1>
				<p class="text-xl md:text-2xl mb-8 text-blue-100">
					{#if $currentLanguage === 'en'}
						Your trusted online store for quality products
					{:else}
						فرۆشگای ئۆنلاینی متمانەپێکراو بۆ بەرهەمی کوالیتی
					{/if}
				</p>
				<div class="flex flex-col sm:flex-row gap-4 justify-center">
					<a href="/products" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
						{#if $currentLanguage === 'en'}
							Shop Now
						{:else}
							ئێستا بکڕە
						{/if}
					</a>
					<a href="/categories" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
						{#if $currentLanguage === 'en'}
							Browse Categories
						{:else}
							گەڕان لە جۆرەکان
						{/if}
					</a>
				</div>
			</div>
		</div>
	</div>

	<!-- Features Section -->
	<div class="py-16 bg-gray-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-12">
				<h2 class="text-3xl font-bold text-gray-900 mb-4">
					{#if $currentLanguage === 'en'}
						Why Choose Coco UK?
					{:else}
						بۆچی کۆکۆ یوکەی هەڵبژێریت؟
					{/if}
				</h2>
				<p class="text-lg text-gray-600">
					{#if $currentLanguage === 'en'}
						We provide the best shopping experience with quality products and excellent service
					{:else}
						ئێمە باشترین ئەزموونی کڕین پێشکەش دەکەین لەگەڵ بەرهەمی کوالیتی و خزمەتگوزاری نایاب
					{/if}
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
				<!-- Feature 1 -->
				<div class="text-center">
					<div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
						<ShoppingBag class="text-blue-600" size={32} />
					</div>
					<h3 class="text-xl font-semibold mb-2">
						{#if $currentLanguage === 'en'}
							Quality Products
						{:else}
							بەرهەمی کوالیتی
						{/if}
					</h3>
					<p class="text-gray-600">
						{#if $currentLanguage === 'en'}
							Carefully selected products from trusted suppliers
						{:else}
							بەرهەمی بە وردی هەڵبژێردراو لە دابینکەری متمانەپێکراو
						{/if}
					</p>
				</div>

				<!-- Feature 2 -->
				<div class="text-center">
					<div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
						<Truck class="text-green-600" size={32} />
					</div>
					<h3 class="text-xl font-semibold mb-2">
						{#if $currentLanguage === 'en'}
							Fast Delivery
						{:else}
							گەیاندنی خێرا
						{/if}
					</h3>
					<p class="text-gray-600">
						{#if $currentLanguage === 'en'}
							Quick and reliable delivery across the UK
						{:else}
							گەیاندنی خێرا و متمانەپێکراو بە تەواوی یوکەی
						{/if}
					</p>
				</div>

				<!-- Feature 3 -->
				<div class="text-center">
					<div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
						<Shield class="text-purple-600" size={32} />
					</div>
					<h3 class="text-xl font-semibold mb-2">
						{#if $currentLanguage === 'en'}
							Secure Shopping
						{:else}
							کڕینی پارێزراو
						{/if}
					</h3>
					<p class="text-gray-600">
						{#if $currentLanguage === 'en'}
							Your data and payments are always protected
						{:else}
							زانیاری و پارەکانت هەمیشە پارێزراون
						{/if}
					</p>
				</div>

				<!-- Feature 4 -->
				<div class="text-center">
					<div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
						<Star class="text-yellow-600" size={32} />
					</div>
					<h3 class="text-xl font-semibold mb-2">
						{#if $currentLanguage === 'en'}
							Customer Support
						{:else}
							پشتگیری کڕیار
						{/if}
					</h3>
					<p class="text-gray-600">
						{#if $currentLanguage === 'en'}
							24/7 customer support in your language
						{:else}
							پشتگیری کڕیار ٢٤/٧ بە زمانی خۆت
						{/if}
					</p>
				</div>
			</div>
		</div>
	</div>

	<!-- Featured Products Section -->
	<div class="py-16">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-12">
				<h2 class="text-3xl font-bold text-gray-900 mb-4">
					{#if $currentLanguage === 'en'}
						Featured Products
					{:else}
						بەرهەمە تایبەتەکان
					{/if}
				</h2>
				<p class="text-lg text-gray-600">
					{#if $currentLanguage === 'en'}
						Discover our most popular products
					{:else}
						بەرهەمە بەناوبانگەکانمان بدۆزەرەوە
					{/if}
				</p>
			</div>

			<!-- Placeholder for featured products -->
			<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
				{#each Array(4) as _, i}
					<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
						<div class="bg-gray-200 h-48 flex items-center justify-center">
							<span class="text-gray-500">Product Image</span>
						</div>
						<div class="p-4">
							<h3 class="font-semibold text-lg mb-2">Sample Product {i + 1}</h3>
							<p class="text-gray-600 text-sm mb-3">Product description goes here...</p>
							<div class="flex items-center justify-between">
								<span class="text-xl font-bold text-blue-600">£{(Math.random() * 100 + 10).toFixed(2)}</span>
								<button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
									{#if $currentLanguage === 'en'}
										Add to Cart
									{:else}
										زیادکردن بۆ سەبەتە
									{/if}
								</button>
							</div>
						</div>
					</div>
				{/each}
			</div>

			<div class="text-center mt-8">
				<a href="/products" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
					{#if $currentLanguage === 'en'}
						View All Products
					{:else}
						بینینی هەموو بەرهەمەکان
					{/if}
				</a>
			</div>
		</div>
	</div>
</div>
