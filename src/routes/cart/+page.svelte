<script lang="ts">
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { cartItems, cartTotal, cartCount, updateQuantity, removeFromCart, clearCart } from '$lib/stores/cart.js';
	import { Trash2, Plus, Minus, ShoppingBag } from 'lucide-svelte';
	
	function handleQuantityChange(productId: number, newQuantity: number) {
		if (newQuantity < 1) {
			removeFromCart(productId);
		} else {
			updateQuantity(productId, newQuantity);
		}
	}
	
	function handleRemoveItem(productId: number) {
		removeFromCart(productId);
	}
	
	function handleClearCart() {
		if (confirm($currentLanguage === 'en' ? 'Are you sure you want to clear your cart?' : 'دڵنیایت لە پاکردنەوەی سەبەتەکەت؟')) {
			clearCart();
		}
	}
</script>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<!-- <PERSON>er -->
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">
			{#if $currentLanguage === 'en'}
				Shopping Cart
			{:else}
				سەبەتەی کڕین
			{/if}
		</h1>
		<p class="text-lg text-gray-600">
			{#if $currentLanguage === 'en'}
				Review your items before checkout
			{:else}
				بەرهەمەکانت پێداچوونەوە بکە پێش پارەدان
			{/if}
		</p>
	</div>
	
	{#if $cartItems.length === 0}
		<!-- Empty Cart -->
		<div class="text-center py-16">
			<div class="bg-gray-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6">
				<ShoppingBag class="text-gray-400" size={48} />
			</div>
			<h2 class="text-2xl font-semibold text-gray-900 mb-4">
				{#if $currentLanguage === 'en'}
					Your cart is empty
				{:else}
					سەبەتەکەت بەتاڵە
				{/if}
			</h2>
			<p class="text-gray-600 mb-8">
				{#if $currentLanguage === 'en'}
					Looks like you haven't added any items to your cart yet.
				{:else}
					وادیارە هێشتا هیچ بەرهەمێکت نەخستووەتە سەبەتەکەت.
				{/if}
			</p>
			<a href="/products" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
				{#if $currentLanguage === 'en'}
					Continue Shopping
				{:else}
					بەردەوامبوون لە کڕین
				{/if}
			</a>
		</div>
	{:else}
		<!-- Cart Items -->
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
			<!-- Items List -->
			<div class="lg:col-span-2">
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="px-6 py-4 border-b border-gray-200">
						<div class="flex justify-between items-center">
							<h2 class="text-lg font-semibold">
								{#if $currentLanguage === 'en'}
									Cart Items ({$cartCount})
								{:else}
									بەرهەمەکانی سەبەتە ({$cartCount})
								{/if}
							</h2>
							<button
								on:click={handleClearCart}
								class="text-red-600 hover:text-red-800 text-sm font-medium"
							>
								{#if $currentLanguage === 'en'}
									Clear Cart
								{:else}
									پاکردنەوەی سەبەتە
								{/if}
							</button>
						</div>
					</div>
					
					<div class="divide-y divide-gray-200">
						{#each $cartItems as item}
							<div class="px-6 py-4">
								<div class="flex items-center space-x-4">
									<!-- Product Image -->
									<div class="bg-gray-200 w-16 h-16 rounded-md flex items-center justify-center flex-shrink-0">
										<span class="text-gray-500 text-xs">IMG</span>
									</div>
									
									<!-- Product Details -->
									<div class="flex-1 min-w-0">
										<h3 class="text-lg font-medium text-gray-900 truncate">{item.name}</h3>
										<p class="text-sm text-gray-500">
											{#if $currentLanguage === 'en'}
												Price: £{item.price.toFixed(2)}
											{:else}
												نرخ: £{item.price.toFixed(2)}
											{/if}
										</p>
									</div>
									
									<!-- Quantity Controls -->
									<div class="flex items-center space-x-2">
										<button
											on:click={() => handleQuantityChange(item.id, item.quantity - 1)}
											class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center"
										>
											<Minus size={16} />
										</button>
										<span class="w-12 text-center font-medium">{item.quantity}</span>
										<button
											on:click={() => handleQuantityChange(item.id, item.quantity + 1)}
											class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center"
										>
											<Plus size={16} />
										</button>
									</div>
									
									<!-- Item Total -->
									<div class="text-right">
										<p class="text-lg font-semibold text-gray-900">
											£{(item.price * item.quantity).toFixed(2)}
										</p>
									</div>
									
									<!-- Remove Button -->
									<button
										on:click={() => handleRemoveItem(item.id)}
										class="text-red-600 hover:text-red-800 p-1"
									>
										<Trash2 size={20} />
									</button>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
			
			<!-- Order Summary -->
			<div class="lg:col-span-1">
				<div class="bg-white rounded-lg shadow-md p-6 sticky top-8">
					<h2 class="text-lg font-semibold mb-4">
						{#if $currentLanguage === 'en'}
							Order Summary
						{:else}
							کورتەی داواکاری
						{/if}
					</h2>
					
					<div class="space-y-3 mb-6">
						<div class="flex justify-between">
							<span class="text-gray-600">
								{#if $currentLanguage === 'en'}
									Subtotal ({$cartCount} items)
								{:else}
									کۆی بەرهەمەکان ({$cartCount} بەرهەم)
								{/if}
							</span>
							<span class="font-medium">£{$cartTotal.toFixed(2)}</span>
						</div>
						
						<div class="flex justify-between">
							<span class="text-gray-600">
								{#if $currentLanguage === 'en'}
									Shipping
								{:else}
									گەیاندن
								{/if}
							</span>
							<span class="font-medium text-green-600">
								{#if $currentLanguage === 'en'}
									Free
								{:else}
									بەخۆڕایی
								{/if}
							</span>
						</div>
						
						<div class="flex justify-between">
							<span class="text-gray-600">
								{#if $currentLanguage === 'en'}
									Tax
								{:else}
									باج
								{/if}
							</span>
							<span class="font-medium">£{($cartTotal * 0.2).toFixed(2)}</span>
						</div>
						
						<div class="border-t pt-3">
							<div class="flex justify-between">
								<span class="text-lg font-semibold">
									{#if $currentLanguage === 'en'}
										Total
									{:else}
										کۆی گشتی
									{/if}
								</span>
								<span class="text-lg font-bold text-blue-600">
									£{($cartTotal * 1.2).toFixed(2)}
								</span>
							</div>
						</div>
					</div>
					
					<div class="space-y-3">
						<button class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
							{#if $currentLanguage === 'en'}
								Proceed to Checkout
							{:else}
								چوون بۆ پارەدان
							{/if}
						</button>
						
						<a href="/products" class="block w-full text-center bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors">
							{#if $currentLanguage === 'en'}
								Continue Shopping
							{:else}
								بەردەوامبوون لە کڕین
							{/if}
						</a>
					</div>
					
					<!-- Security Notice -->
					<div class="mt-6 p-4 bg-green-50 rounded-lg">
						<p class="text-sm text-green-800">
							{#if $currentLanguage === 'en'}
								🔒 Secure checkout with SSL encryption
							{:else}
								🔒 پارەدانی پارێزراو لەگەڵ کۆدکردنی SSL
							{/if}
						</p>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
