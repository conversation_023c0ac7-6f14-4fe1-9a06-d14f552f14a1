<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { user, loading, signOut } from '$lib/stores/auth.js';
	import { currentLanguage, setLanguage, t } from '$lib/stores/language.js';
	import { cartCount } from '$lib/stores/cart.js';
	import { Menu, X, ShoppingCart, User, Globe, LogOut } from 'lucide-svelte';

	let { children } = $props();

	let mobileMenuOpen = $state(false);
	let userMenuOpen = $state(false);
	let languageMenuOpen = $state(false);

	// Close menus when clicking outside
	function closeMenus() {
		mobileMenuOpen = false;
		userMenuOpen = false;
		languageMenuOpen = false;
	}

	// Handle language change
	function handleLanguageChange(lang: string) {
		setLanguage(lang);
		closeMenus();
	}

	// Handle logout
	async function handleLogout() {
		await signOut();
		closeMenus();
	}

	onMount(() => {
		// Close menus when clicking outside
		document.addEventListener('click', closeMenus);
		return () => {
			document.removeEventListener('click', closeMenus);
		};
	});
</script>

<div class="min-h-screen bg-gray-50">
	<!-- Navigation -->
	<nav class="bg-white shadow-lg sticky top-0 z-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between h-16">
				<!-- Logo and main nav -->
				<div class="flex items-center">
					<!-- Logo -->
					<a href="/" class="flex items-center space-x-2">
						<img src="/logo/logo.svg" alt="Coco UK" class="h-8 w-8" />
						<span class="text-xl font-bold text-gray-900">Coco UK</span>
					</a>

					<!-- Desktop Navigation -->
					<div class="hidden md:ml-8 md:flex md:space-x-8">
						<a href="/" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
							{t('nav.home', $currentLanguage)}
						</a>
						<a href="/products" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
							{t('nav.products', $currentLanguage)}
						</a>
						<a href="/categories" class="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
							{t('nav.categories', $currentLanguage)}
						</a>
					</div>
				</div>

				<!-- Right side navigation -->
				<div class="flex items-center space-x-4">
					<!-- Language Switcher -->
					<div class="relative">
						<button
							onclick={(e) => { e.stopPropagation(); languageMenuOpen = !languageMenuOpen; }}
							class="flex items-center space-x-1 text-gray-700 hover:text-blue-600"
						>
							<Globe size={20} />
							<span class="hidden sm:block text-sm">
								{$currentLanguage === 'en' ? 'EN' : 'کو'}
							</span>
						</button>
						{#if languageMenuOpen}
							<div class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg py-1 z-50">
								<button
									onclick={() => handleLanguageChange('en')}
									class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
									class:bg-blue-50={$currentLanguage === 'en'}
								>
									English
								</button>
								<button
									onclick={() => handleLanguageChange('ku')}
									class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
									class:bg-blue-50={$currentLanguage === 'ku'}
								>
									کوردی سۆرانی
								</button>
							</div>
						{/if}
					</div>

					<!-- Cart -->
					<a href="/cart" class="relative text-gray-700 hover:text-blue-600">
						<ShoppingCart size={24} />
						{#if $cartCount > 0}
							<span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
								{$cartCount}
							</span>
						{/if}
					</a>

					<!-- User Menu -->
					{#if !$loading}
						{#if $user}
							<div class="relative">
								<button
									onclick={(e) => { e.stopPropagation(); userMenuOpen = !userMenuOpen; }}
									class="flex items-center space-x-1 text-gray-700 hover:text-blue-600"
								>
									<User size={24} />
								</button>
								{#if userMenuOpen}
									<div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
										<a href="/account" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
											{t('nav.account', $currentLanguage)}
										</a>
										{#if $user.email === '<EMAIL>'}
											<a href="/admin" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
												{t('nav.admin', $currentLanguage)}
											</a>
										{/if}
										<button
											onclick={handleLogout}
											class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
										>
											<div class="flex items-center space-x-2">
												<LogOut size={16} />
												<span>{t('nav.logout', $currentLanguage)}</span>
											</div>
										</button>
									</div>
								{/if}
							</div>
						{:else}
							<div class="flex items-center space-x-2">
								<a href="/login" class="text-gray-700 hover:text-blue-600 text-sm font-medium">
									{t('nav.login', $currentLanguage)}
								</a>
								<a href="/register" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
									{t('nav.register', $currentLanguage)}
								</a>
							</div>
						{/if}
					{/if}

					<!-- Mobile menu button -->
					<button
						onclick={(e) => { e.stopPropagation(); mobileMenuOpen = !mobileMenuOpen; }}
						class="md:hidden text-gray-700 hover:text-blue-600"
					>
						{#if mobileMenuOpen}
							<X size={24} />
						{:else}
							<Menu size={24} />
						{/if}
					</button>
				</div>
			</div>
		</div>

		<!-- Mobile Navigation -->
		{#if mobileMenuOpen}
			<div class="md:hidden bg-white border-t border-gray-200">
				<div class="px-2 pt-2 pb-3 space-y-1">
					<a href="/" class="block px-3 py-2 text-base font-medium text-gray-900 hover:text-blue-600">
						{t('nav.home', $currentLanguage)}
					</a>
					<a href="/products" class="block px-3 py-2 text-base font-medium text-gray-900 hover:text-blue-600">
						{t('nav.products', $currentLanguage)}
					</a>
					<a href="/categories" class="block px-3 py-2 text-base font-medium text-gray-900 hover:text-blue-600">
						{t('nav.categories', $currentLanguage)}
					</a>
				</div>
			</div>
		{/if}
	</nav>

	<!-- Main Content -->
	<main>
		{@render children()}
	</main>

	<!-- Footer -->
	<footer class="bg-gray-900 text-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
			<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
				<!-- Company Info -->
				<div class="col-span-1 md:col-span-2">
					<div class="flex items-center space-x-2 mb-4">
						<img src="/logo/logo.svg" alt="Coco UK" class="h-8 w-8" />
						<span class="text-xl font-bold">{t('footer.company', $currentLanguage)}</span>
					</div>
					<p class="text-gray-400 mb-4">
						{t('footer.description', $currentLanguage)}
					</p>
				</div>

				<!-- Quick Links -->
				<div>
					<h3 class="text-lg font-semibold mb-4">Quick Links</h3>
					<ul class="space-y-2">
						<li><a href="/about" class="text-gray-400 hover:text-white">{t('footer.about', $currentLanguage)}</a></li>
						<li><a href="/contact" class="text-gray-400 hover:text-white">{t('footer.contact', $currentLanguage)}</a></li>
						<li><a href="/privacy" class="text-gray-400 hover:text-white">{t('footer.privacy', $currentLanguage)}</a></li>
						<li><a href="/terms" class="text-gray-400 hover:text-white">{t('footer.terms', $currentLanguage)}</a></li>
					</ul>
				</div>

				<!-- Contact Info -->
				<div>
					<h3 class="text-lg font-semibold mb-4">{t('footer.contact', $currentLanguage)}</h3>
					<div class="text-gray-400 space-y-2">
						<p>Email: <EMAIL></p>
						<p>Phone: +44 ************</p>
					</div>
				</div>
			</div>

			<div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
				<p>&copy; 2024 Coco UK. All rights reserved.</p>
			</div>
		</div>
	</footer>
</div>
