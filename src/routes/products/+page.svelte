<script lang="ts">
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { addToCart } from '$lib/stores/cart.js';
	import { Search, Filter, Grid, List, Star } from 'lucide-svelte';
	
	// Sample products data (in a real app, this would come from Supabase)
	let products = [
		{
			id: 1,
			name: { en: 'Premium Headphones', ku: 'هێدفۆنی پلەی یەک' },
			description: { en: 'High-quality wireless headphones with noise cancellation', ku: 'هێدفۆنی بێ وایەری کوالیتی بەرز لەگەڵ لابردنی دەنگی ناخۆش' },
			price: 89.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Electronics', ku: 'ئەلیکترۆنیات' },
			rating: 4.5,
			inStock: true
		},
		{
			id: 2,
			name: { en: 'Organic Coffee Beans', ku: 'دانەی قاوەی ئۆرگانیک' },
			description: { en: 'Premium organic coffee beans from Ethiopia', ku: 'دانەی قاوەی ئۆرگانیکی پلەی یەک لە ئەتیۆپیاوە' },
			price: 24.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Food & Beverages', ku: 'خواردن و خواردنەوە' },
			rating: 4.8,
			inStock: true
		},
		{
			id: 3,
			name: { en: 'Yoga Mat', ku: 'پاڵاسی یۆگا' },
			description: { en: 'Non-slip yoga mat for all types of exercise', ku: 'پاڵاسی یۆگای نەخلیسکێن بۆ هەموو جۆرەکانی ڕاهێنان' },
			price: 34.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Sports & Fitness', ku: 'وەرزش و تەندروستی' },
			rating: 4.3,
			inStock: true
		},
		{
			id: 4,
			name: { en: 'Smart Watch', ku: 'کاتژمێری زیرەک' },
			description: { en: 'Advanced smartwatch with health monitoring', ku: 'کاتژمێری زیرەکی پێشکەوتوو لەگەڵ چاودێری تەندروستی' },
			price: 199.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Electronics', ku: 'ئەلیکترۆنیات' },
			rating: 4.6,
			inStock: false
		},
		{
			id: 5,
			name: { en: 'Leather Wallet', ku: 'جزدانی چەرم' },
			description: { en: 'Handcrafted genuine leather wallet', ku: 'جزدانی چەرمی ڕاستەقینەی دەستکرد' },
			price: 45.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Fashion', ku: 'مۆدا' },
			rating: 4.4,
			inStock: true
		},
		{
			id: 6,
			name: { en: 'Bluetooth Speaker', ku: 'بڵندگۆی بلوتووس' },
			description: { en: 'Portable wireless speaker with excellent sound quality', ku: 'بڵندگۆی بێ وایەری هەڵگرتوو لەگەڵ کوالیتی دەنگی نایاب' },
			price: 59.99,
			image: '/api/placeholder/300/300',
			category: { en: 'Electronics', ku: 'ئەلیکترۆنیات' },
			rating: 4.2,
			inStock: true
		}
	];
	
	let searchTerm = '';
	let selectedCategory = '';
	let viewMode = 'grid'; // 'grid' or 'list'
	let sortBy = 'name'; // 'name', 'price', 'rating'
	
	// Get unique categories
	$: categories = [...new Set(products.map(p => p.category[$currentLanguage]))];
	
	// Filter and sort products
	$: filteredProducts = products
		.filter(product => {
			const matchesSearch = product.name[$currentLanguage].toLowerCase().includes(searchTerm.toLowerCase()) ||
								product.description[$currentLanguage].toLowerCase().includes(searchTerm.toLowerCase());
			const matchesCategory = !selectedCategory || product.category[$currentLanguage] === selectedCategory;
			return matchesSearch && matchesCategory;
		})
		.sort((a, b) => {
			switch (sortBy) {
				case 'price':
					return a.price - b.price;
				case 'rating':
					return b.rating - a.rating;
				default:
					return a.name[$currentLanguage].localeCompare(b.name[$currentLanguage]);
			}
		});
	
	function handleAddToCart(product: any) {
		addToCart({
			id: product.id,
			name: product.name[$currentLanguage],
			price: product.price,
			image: product.image
		});
	}
	
	function renderStars(rating: number) {
		const stars = [];
		const fullStars = Math.floor(rating);
		const hasHalfStar = rating % 1 !== 0;
		
		for (let i = 0; i < fullStars; i++) {
			stars.push('★');
		}
		if (hasHalfStar) {
			stars.push('☆');
		}
		return stars.join('');
	}
</script>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<!-- Page Header -->
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">
			{#if $currentLanguage === 'en'}
				Products
			{:else}
				بەرهەمەکان
			{/if}
		</h1>
		<p class="text-lg text-gray-600">
			{#if $currentLanguage === 'en'}
				Discover our wide range of quality products
			{:else}
				کۆمەڵێک بەرهەمی کوالیتیمان بدۆزەرەوە
			{/if}
		</p>
	</div>
	
	<!-- Search and Filters -->
	<div class="bg-white rounded-lg shadow-md p-6 mb-8">
		<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
			<!-- Search -->
			<div class="relative">
				<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
					<Search class="h-5 w-5 text-gray-400" />
				</div>
				<input
					type="text"
					bind:value={searchTerm}
					placeholder={$currentLanguage === 'en' ? 'Search products...' : 'گەڕان لە بەرهەمەکان...'}
					class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
				/>
			</div>
			
			<!-- Category Filter -->
			<div>
				<select
					bind:value={selectedCategory}
					class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
				>
					<option value="">
						{#if $currentLanguage === 'en'}
							All Categories
						{:else}
							هەموو جۆرەکان
						{/if}
					</option>
					{#each categories as category}
						<option value={category}>{category}</option>
					{/each}
				</select>
			</div>
			
			<!-- Sort -->
			<div>
				<select
					bind:value={sortBy}
					class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
				>
					<option value="name">
						{#if $currentLanguage === 'en'}
							Sort by Name
						{:else}
							ڕیزکردن بە ناو
						{/if}
					</option>
					<option value="price">
						{#if $currentLanguage === 'en'}
							Sort by Price
						{:else}
							ڕیزکردن بە نرخ
						{/if}
					</option>
					<option value="rating">
						{#if $currentLanguage === 'en'}
							Sort by Rating
						{:else}
							ڕیزکردن بە هەڵسەنگاندن
						{/if}
					</option>
				</select>
			</div>
			
			<!-- View Mode -->
			<div class="flex space-x-2">
				<button
					on:click={() => viewMode = 'grid'}
					class="flex-1 flex items-center justify-center px-3 py-2 border rounded-md"
					class:bg-blue-600={viewMode === 'grid'}
					class:text-white={viewMode === 'grid'}
					class:border-blue-600={viewMode === 'grid'}
					class:bg-white={viewMode !== 'grid'}
					class:text-gray-700={viewMode !== 'grid'}
					class:border-gray-300={viewMode !== 'grid'}
				>
					<Grid size={20} />
				</button>
				<button
					on:click={() => viewMode = 'list'}
					class="flex-1 flex items-center justify-center px-3 py-2 border rounded-md"
					class:bg-blue-600={viewMode === 'list'}
					class:text-white={viewMode === 'list'}
					class:border-blue-600={viewMode === 'list'}
					class:bg-white={viewMode !== 'list'}
					class:text-gray-700={viewMode !== 'list'}
					class:border-gray-300={viewMode !== 'list'}
				>
					<List size={20} />
				</button>
			</div>
		</div>
	</div>
	
	<!-- Products Grid/List -->
	<div class="mb-4 text-sm text-gray-600">
		{#if $currentLanguage === 'en'}
			Showing {filteredProducts.length} products
		{:else}
			پیشاندانی {filteredProducts.length} بەرهەم
		{/if}
	</div>
	
	{#if viewMode === 'grid'}
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
			{#each filteredProducts as product}
				<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Product Image</span>
					</div>
					<div class="p-4">
						<h3 class="font-semibold text-lg mb-2">{product.name[$currentLanguage]}</h3>
						<p class="text-gray-600 text-sm mb-2">{product.category[$currentLanguage]}</p>
						<p class="text-gray-600 text-sm mb-3 line-clamp-2">{product.description[$currentLanguage]}</p>
						
						<!-- Rating -->
						<div class="flex items-center mb-3">
							<span class="text-yellow-400 mr-1">{renderStars(product.rating)}</span>
							<span class="text-sm text-gray-600">({product.rating})</span>
						</div>
						
						<div class="flex items-center justify-between">
							<span class="text-xl font-bold text-blue-600">£{product.price.toFixed(2)}</span>
							{#if product.inStock}
								<button
									on:click={() => handleAddToCart(product)}
									class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
								>
									{#if $currentLanguage === 'en'}
										Add to Cart
									{:else}
										زیادکردن بۆ سەبەتە
									{/if}
								</button>
							{:else}
								<span class="text-red-500 text-sm font-medium">
									{#if $currentLanguage === 'en'}
										Out of Stock
									{:else}
										لە کۆگا نییە
									{/if}
								</span>
							{/if}
						</div>
					</div>
				</div>
			{/each}
		</div>
	{:else}
		<div class="space-y-4">
			{#each filteredProducts as product}
				<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
					<div class="flex">
						<div class="bg-gray-200 w-32 h-32 flex items-center justify-center flex-shrink-0">
							<span class="text-gray-500 text-sm">Image</span>
						</div>
						<div class="flex-1 p-4">
							<div class="flex justify-between items-start">
								<div class="flex-1">
									<h3 class="font-semibold text-lg mb-1">{product.name[$currentLanguage]}</h3>
									<p class="text-gray-600 text-sm mb-2">{product.category[$currentLanguage]}</p>
									<p class="text-gray-600 text-sm mb-3">{product.description[$currentLanguage]}</p>
									
									<!-- Rating -->
									<div class="flex items-center mb-3">
										<span class="text-yellow-400 mr-1">{renderStars(product.rating)}</span>
										<span class="text-sm text-gray-600">({product.rating})</span>
									</div>
								</div>
								<div class="text-right ml-4">
									<div class="text-xl font-bold text-blue-600 mb-2">£{product.price.toFixed(2)}</div>
									{#if product.inStock}
										<button
											on:click={() => handleAddToCart(product)}
											class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
										>
											{#if $currentLanguage === 'en'}
												Add to Cart
											{:else}
												زیادکردن بۆ سەبەتە
											{/if}
										</button>
									{:else}
										<span class="text-red-500 text-sm font-medium">
											{#if $currentLanguage === 'en'}
												Out of Stock
											{:else}
												لە کۆگا نییە
											{/if}
										</span>
									{/if}
								</div>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
	
	{#if filteredProducts.length === 0}
		<div class="text-center py-12">
			<p class="text-gray-500 text-lg">
				{#if $currentLanguage === 'en'}
					No products found matching your criteria.
				{:else}
					هیچ بەرهەمێک نەدۆزرایەوە کە لەگەڵ مەرجەکانت بگونجێت.
				{/if}
			</p>
		</div>
	{/if}
</div>
