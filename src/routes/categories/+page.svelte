<script lang="ts">
	import { currentLanguage, t } from '$lib/stores/language.js';
	import { Smartphone, Coffee, Dumbbell, Shirt, Home, Book } from 'lucide-svelte';
	
	// Sample categories data (in a real app, this would come from Supabase)
	let categories = [
		{
			id: 1,
			name: { en: 'Electronics', ku: 'ئەلیکترۆنیات' },
			description: { en: 'Latest gadgets and electronic devices', ku: 'نوێترین ئامێرەکان و ئامێرە ئەلیکترۆنیەکان' },
			icon: Smartphone,
			productCount: 15,
			image: '/api/placeholder/400/300'
		},
		{
			id: 2,
			name: { en: 'Food & Beverages', ku: 'خواردن و خواردنەوە' },
			description: { en: 'Organic food, coffee, tea and beverages', ku: 'خواردنی ئۆرگانیک، قاوە، چا و خواردنەوەکان' },
			icon: Coffee,
			productCount: 8,
			image: '/api/placeholder/400/300'
		},
		{
			id: 3,
			name: { en: 'Sports & Fitness', ku: 'وەرزش و تەندروستی' },
			description: { en: 'Exercise equipment and sports gear', ku: 'ئامێری ڕاهێنان و کەرەستەی وەرزشی' },
			icon: Dumbbell,
			productCount: 12,
			image: '/api/placeholder/400/300'
		},
		{
			id: 4,
			name: { en: 'Fashion', ku: 'مۆدا' },
			description: { en: 'Clothing, accessories and fashion items', ku: 'جل و بەرگ، ئەکسسوارات و بابەتەکانی مۆدا' },
			icon: Shirt,
			productCount: 25,
			image: '/api/placeholder/400/300'
		},
		{
			id: 5,
			name: { en: 'Home & Garden', ku: 'ماڵ و باخچە' },
			description: { en: 'Home decor, furniture and garden supplies', ku: 'ڕازاندنەوەی ماڵ، کەلوپەل و پێداویستیەکانی باخچە' },
			icon: Home,
			productCount: 18,
			image: '/api/placeholder/400/300'
		},
		{
			id: 6,
			name: { en: 'Books & Media', ku: 'کتێب و میدیا' },
			description: { en: 'Books, magazines and digital media', ku: 'کتێب، گۆڤار و میدیای دیجیتاڵ' },
			icon: Book,
			productCount: 9,
			image: '/api/placeholder/400/300'
		}
	];
</script>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<!-- Page Header -->
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">
			{#if $currentLanguage === 'en'}
				Product Categories
			{:else}
				جۆرەکانی بەرهەم
			{/if}
		</h1>
		<p class="text-lg text-gray-600">
			{#if $currentLanguage === 'en'}
				Browse our wide range of product categories
			{:else}
				گەڕان لە کۆمەڵێک جۆری بەرهەمی جیاواز
			{/if}
		</p>
	</div>
	
	<!-- Categories Grid -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
		{#each categories as category}
			<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
				<!-- Category Image -->
				<div class="bg-gradient-to-br from-blue-500 to-purple-600 h-48 flex items-center justify-center relative overflow-hidden">
					<div class="absolute inset-0 bg-black bg-opacity-20"></div>
					<div class="relative z-10 text-center">
						<div class="bg-white bg-opacity-20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
							<svelte:component this={category.icon} class="text-white" size={32} />
						</div>
						<h3 class="text-xl font-bold text-white">{category.name[$currentLanguage]}</h3>
					</div>
				</div>
				
				<!-- Category Content -->
				<div class="p-6">
					<p class="text-gray-600 mb-4">{category.description[$currentLanguage]}</p>
					
					<div class="flex items-center justify-between mb-4">
						<span class="text-sm text-gray-500">
							{#if $currentLanguage === 'en'}
								{category.productCount} products
							{:else}
								{category.productCount} بەرهەم
							{/if}
						</span>
						<span class="text-sm font-medium text-blue-600">
							{#if $currentLanguage === 'en'}
								View All
							{:else}
								بینینی هەموو
							{/if}
						</span>
					</div>
					
					<a 
						href="/products?category={encodeURIComponent(category.name[$currentLanguage])}"
						class="block w-full bg-blue-600 text-white text-center py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors group-hover:bg-blue-700"
					>
						{#if $currentLanguage === 'en'}
							Browse {category.name[$currentLanguage]}
						{:else}
							گەڕان لە {category.name[$currentLanguage]}
						{/if}
					</a>
				</div>
			</div>
		{/each}
	</div>
	
	<!-- Featured Categories Section -->
	<div class="mt-16">
		<div class="text-center mb-12">
			<h2 class="text-3xl font-bold text-gray-900 mb-4">
				{#if $currentLanguage === 'en'}
					Popular Categories
				{:else}
					جۆرە بەناوبانگەکان
				{/if}
			</h2>
			<p class="text-lg text-gray-600">
				{#if $currentLanguage === 'en'}
					Most popular categories among our customers
				{:else}
					جۆرە بەناوبانگترینەکان لەنێو کڕیارەکانمان
				{/if}
			</p>
		</div>
		
		<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
			<!-- Featured Category 1 -->
			<div class="relative bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg overflow-hidden">
				<div class="absolute inset-0 bg-black bg-opacity-30"></div>
				<div class="relative z-10 p-8 text-white">
					<Smartphone class="mb-4" size={48} />
					<h3 class="text-2xl font-bold mb-2">
						{#if $currentLanguage === 'en'}
							Electronics
						{:else}
							ئەلیکترۆنیات
						{/if}
					</h3>
					<p class="mb-4 text-blue-100">
						{#if $currentLanguage === 'en'}
							Latest smartphones, laptops, and gadgets
						{:else}
							نوێترین مۆبایل، لاپتۆپ و ئامێرەکان
						{/if}
					</p>
					<a href="/products?category=Electronics" class="inline-flex items-center text-white font-medium hover:text-blue-200">
						{#if $currentLanguage === 'en'}
							Shop Now →
						{:else}
							ئێستا بکڕە ←
						{/if}
					</a>
				</div>
			</div>
			
			<!-- Featured Category 2 -->
			<div class="relative bg-gradient-to-r from-green-600 to-green-800 rounded-lg overflow-hidden">
				<div class="absolute inset-0 bg-black bg-opacity-30"></div>
				<div class="relative z-10 p-8 text-white">
					<Dumbbell class="mb-4" size={48} />
					<h3 class="text-2xl font-bold mb-2">
						{#if $currentLanguage === 'en'}
							Sports & Fitness
						{:else}
							وەرزش و تەندروستی
						{/if}
					</h3>
					<p class="mb-4 text-green-100">
						{#if $currentLanguage === 'en'}
							Exercise equipment and sports gear
						{:else}
							ئامێری ڕاهێنان و کەرەستەی وەرزشی
						{/if}
					</p>
					<a href="/products?category=Sports" class="inline-flex items-center text-white font-medium hover:text-green-200">
						{#if $currentLanguage === 'en'}
							Shop Now →
						{:else}
							ئێستا بکڕە ←
						{/if}
					</a>
				</div>
			</div>
			
			<!-- Featured Category 3 -->
			<div class="relative bg-gradient-to-r from-purple-600 to-purple-800 rounded-lg overflow-hidden">
				<div class="absolute inset-0 bg-black bg-opacity-30"></div>
				<div class="relative z-10 p-8 text-white">
					<Shirt class="mb-4" size={48} />
					<h3 class="text-2xl font-bold mb-2">
						{#if $currentLanguage === 'en'}
							Fashion
						{:else}
							مۆدا
						{/if}
					</h3>
					<p class="mb-4 text-purple-100">
						{#if $currentLanguage === 'en'}
							Trendy clothing and accessories
						{:else}
							جل و بەرگ و ئەکسسواری مۆدێرن
						{/if}
					</p>
					<a href="/products?category=Fashion" class="inline-flex items-center text-white font-medium hover:text-purple-200">
						{#if $currentLanguage === 'en'}
							Shop Now →
						{:else}
							ئێستا بکڕە ←
						{/if}
					</a>
				</div>
			</div>
		</div>
	</div>
	
	<!-- Call to Action -->
	<div class="mt-16 bg-gray-100 rounded-lg p-8 text-center">
		<h2 class="text-2xl font-bold text-gray-900 mb-4">
			{#if $currentLanguage === 'en'}
				Can't find what you're looking for?
			{:else}
				ئەوەی دەیەوێت نادۆزیتەوە؟
			{/if}
		</h2>
		<p class="text-gray-600 mb-6">
			{#if $currentLanguage === 'en'}
				Contact our customer support team for assistance
			{:else}
				پەیوەندی بە تیمی پشتگیری کڕیارەوە بکە بۆ یارمەتی
			{/if}
		</p>
		<div class="flex flex-col sm:flex-row gap-4 justify-center">
			<a href="/contact" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
				{#if $currentLanguage === 'en'}
					Contact Support
				{:else}
					پەیوەندی بە پشتگیری
				{/if}
			</a>
			<a href="/products" class="bg-white text-blue-600 border-2 border-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
				{#if $currentLanguage === 'en'}
					Browse All Products
				{:else}
					گەڕان لە هەموو بەرهەمەکان
				{/if}
			</a>
		</div>
	</div>
</div>
